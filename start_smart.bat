@echo off
chcp 65001 >nul
title ComfyUI Smart Launcher
color 0A

echo ====================================
echo        ComfyUI Smart Launcher
echo ====================================
echo.

REM 检查 Python 环境
where python >nul 2>nul
if %errorlevel% neq 0 (
    echo [错误] 未找到 Python
    echo 请从 https://www.python.org/downloads/ 下载并安装 Python
    echo 安装时请勾选"Add Python to PATH"选项
    pause
    exit /b 1
)

REM 显示 Python 版本
python --version
echo.

REM 检查并安装依赖
echo 正在检查依赖...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo [警告] 依赖安装可能不完整，但将继续尝试启动服务
)

echo.
echo ====================================
echo          启动 ComfyUI 服务
echo ====================================
echo.
echo 服务将在启动后通过以下地址访问：
echo http://127.0.0.1:8188
echo.
echo 正在启动服务...请稍候...
echo.

python main.py --port 8188

pause
