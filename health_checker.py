#!/usr/bin/env python3
import requests
import time
import json
from datetime import datetime

def check_comfyui_health():
    """Check ComfyUI server health"""
    url = "http://127.0.0.1:8188"
    
    while True:
        try:
            response = requests.get(f"{url}/system_stats", timeout=5)
            if response.status_code == 200:
                print(f"✓ ComfyUI healthy at {datetime.now().strftime('%H:%M:%S')}")
                status = "healthy"
            else:
                print(f"⚠️  ComfyUI responding but status {response.status_code}")
                status = "warning"
        except requests.exceptions.ConnectionError:
            print(f"❌ ComfyUI not responding at {datetime.now().strftime('%H:%M:%S')}")
            status = "down"
        except Exception as e:
            print(f"❌ Health check error: {e}")
            status = "error"
        
        # Log health status
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "status": status
        }
        
        with open("health_log.jsonl", "a") as f:
            f.write(json.dumps(log_data) + "\n")
        
        time.sleep(60)  # Check every minute

if __name__ == "__main__":
    print("Starting health checker...")
    check_comfyui_health()
