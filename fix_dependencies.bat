@echo off
title ComfyUI Dependencies Fix
color 0E

echo ===============================================
echo        ComfyUI Dependencies Fix
echo ===============================================
echo.
echo Installing missing dependencies for stability...
echo.

echo [1/5] Installing Pillow (Image processing)...
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m pip install Pillow
echo.

echo [2/5] Installing OpenCV (Computer vision)...
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m pip install opencv-python
echo.

echo [3/5] Installing Accelerate (Model acceleration)...
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m pip install accelerate
echo.

echo [4/5] Installing xformers (Memory optimization)...
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m pip install xformers --index-url https://download.pytorch.org/whl/cu121
echo.

echo [5/5] Upgrading pip and checking installation...
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m pip install --upgrade pip
echo.

echo ===============================================
echo Dependencies installation complete!
echo ===============================================
echo.
echo Verifying installation...
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe check_environment.py
echo.
pause
