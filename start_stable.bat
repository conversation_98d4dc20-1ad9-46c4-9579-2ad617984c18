@echo off
title ComfyUI Stable Server
color 0A

echo ==========================================
echo    ComfyUI Stable Mode - RTX 3090 24GB
echo ==========================================
echo.
echo Starting stable ComfyUI server...
echo URL: http://127.0.0.1:8188
echo.

REM Clean up any existing processes
taskkill /f /im python.exe >nul 2>&1
timeout /t 2 >nul

REM Set stable environment variables
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
set CUDA_LAUNCH_BLOCKING=0
set HF_HUB_DISABLE_TELEMETRY=1
set DO_NOT_TRACK=1

echo Starting ComfyUI with stable settings...
echo.

C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --normalvram --use-pytorch-cross-attention --preview-method auto --cache-classic --reserve-vram 6.0 --listen 127.0.0.1 --port 8188 --auto-launch

echo.
echo ==========================================
echo ComfyUI Server Stopped
echo ==========================================
pause
