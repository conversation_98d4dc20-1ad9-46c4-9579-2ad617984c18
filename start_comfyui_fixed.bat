@echo off
chcp 65001 >nul
title ComfyUI Server
color 0A

echo ===============================
echo        ComfyUI Launcher
echo      Stable Configuration
echo ===============================
echo.
echo Cleaning up existing processes...
taskkill /f /im python.exe >nul 2>&1
timeout /t 2 >nul

echo Setting environment variables...
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
set HF_HUB_DISABLE_TELEMETRY=1
set DO_NOT_TRACK=1

echo Starting ComfyUI with stable settings...
echo Server URL: http://127.0.0.1:8188
echo.

C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --normalvram --use-pytorch-cross-attention --preview-method auto --cache-classic --reserve-vram 4.0 --listen 127.0.0.1 --port 8188 --auto-launch

pause
