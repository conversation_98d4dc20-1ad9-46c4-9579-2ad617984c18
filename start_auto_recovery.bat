@echo off
title ComfyUI Auto-Recovery Mode - RTX 3090 24GB
color 0C

:MAIN_LOOP
cls
echo ===============================================
echo      ComfyUI Auto-Recovery Mode
echo        RTX 3090 24GB with Failsafe
echo ===============================================
echo.

REM Initialize restart counter
if not defined restart_count set restart_count=0
set /a restart_count+=1

echo Current Time: %date% %time%
echo Restart Count: %restart_count%
echo.

REM Clean up any existing processes
echo [INFO] Cleaning up existing processes...
taskkill /f /im python.exe >nul 2>&1
timeout /t 5 >nul

REM Set ultra-stable environment
echo [INFO] Setting ultra-stable environment...
call set_stability_env.bat

REM Additional failsafe settings
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128,garbage_collection_threshold:0.95
set CUDA_LAUNCH_BLOCKING=0
set OMP_NUM_THREADS=1
set MKL_NUM_THREADS=1

echo [INFO] Starting ComfyUI (Attempt #%restart_count%)...
echo [INFO] Auto-recovery enabled - will restart on crash
echo [INFO] Press Ctrl+C twice quickly to disable auto-restart
echo.

REM Start memory monitor
start /min "Memory Monitor" C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe memory_monitor.py

REM Launch ComfyUI with failsafe settings
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py ^
    --normalvram ^
    --use-pytorch-cross-attention ^
    --preview-method none ^
    --cache-classic ^
    --reserve-vram 8.0 ^
    --listen 127.0.0.1 ^
    --port 8188 ^
    --verbose INFO ^
    --dont-print-server ^
    --auto-launch

REM If we reach here, ComfyUI has stopped
echo.
echo ===============================================
echo ComfyUI stopped at %time%
echo Restart #%restart_count% - Auto-restarting in 15 seconds...
echo Press Ctrl+C to stop auto-restart
echo ===============================================

REM Clean up background processes
taskkill /f /im python.exe /fi "WINDOWTITLE eq Memory Monitor" >nul 2>&1

REM Wait before restart
timeout /t 15

goto MAIN_LOOP
