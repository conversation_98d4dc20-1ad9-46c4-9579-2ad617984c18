#!/usr/bin/env python3
"""
ComfyUI Environment Diagnostic Tool
Checks for common stability issues
"""

import sys
import os
import psutil
import platform
import subprocess

def check_python():
    print("=== Python Environment ===")
    print(f"Python Version: {sys.version}")
    print(f"Platform: {platform.platform()}")
    print(f"Architecture: {platform.architecture()}")
    print()

def check_pytorch():
    print("=== PyTorch Environment ===")
    try:
        import torch
        print(f"PyTorch Version: {torch.__version__}")
        print(f"CUDA Available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA Version: {torch.version.cuda}")
            print(f"GPU Count: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                print(f"GPU {i}: {props.name}")
                print(f"  Total Memory: {props.total_memory / 1024**3:.1f} GB")
                print(f"  Compute Capability: {props.major}.{props.minor}")
        else:
            print("CUDA not available!")
    except ImportError as e:
        print(f"PyTorch not found: {e}")
    print()

def check_memory():
    print("=== Memory Status ===")
    memory = psutil.virtual_memory()
    print(f"Total RAM: {memory.total / 1024**3:.1f} GB")
    print(f"Available RAM: {memory.available / 1024**3:.1f} GB")
    print(f"Used RAM: {memory.used / 1024**3:.1f} GB")
    print(f"Memory Usage: {memory.percent:.1f}%")
    
    try:
        import torch
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                allocated = torch.cuda.memory_allocated(i) / 1024**3
                reserved = torch.cuda.memory_reserved(i) / 1024**3
                total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"GPU {i} Memory:")
                print(f"  Allocated: {allocated:.1f} GB")
                print(f"  Reserved: {reserved:.1f} GB")
                print(f"  Total: {total:.1f} GB")
                print(f"  Free: {total - reserved:.1f} GB")
    except:
        pass
    print()

def check_processes():
    print("=== Running Processes ===")
    python_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
        try:
            if 'python' in proc.info['name'].lower():
                python_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    if python_processes:
        print("Python processes found:")
        for proc in python_processes:
            memory_mb = proc['memory_info'].rss / 1024 / 1024
            print(f"  PID {proc['pid']}: {proc['name']} - {memory_mb:.1f} MB")
    else:
        print("No Python processes running")
    print()

def check_comfyui_deps():
    print("=== ComfyUI Dependencies ===")
    deps = [
        'torch', 'torchvision', 'torchaudio',
        'numpy', 'pillow', 'opencv-python',
        'transformers', 'accelerate', 'xformers',
        'safetensors', 'psutil'
    ]
    
    for dep in deps:
        try:
            module = __import__(dep)
            version = getattr(module, '__version__', 'unknown')
            print(f"✓ {dep}: {version}")
        except ImportError:
            print(f"✗ {dep}: not installed")
    print()

def check_network():
    print("=== Network Status ===")
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', 8188))
        if result == 0:
            print("✓ Port 8188 is in use (ComfyUI likely running)")
        else:
            print("✗ Port 8188 is free")
        sock.close()
    except Exception as e:
        print(f"Network check failed: {e}")
    print()

def check_stability_issues():
    print("=== Stability Analysis ===")
    issues = []
    
    # Check memory
    memory = psutil.virtual_memory()
    if memory.available / 1024**3 < 4:
        issues.append("Low available RAM (< 4GB)")
    
    # Check PyTorch
    try:
        import torch
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                if props.total_memory / 1024**3 < 8:
                    issues.append(f"GPU {i} has low VRAM (< 8GB)")
        else:
            issues.append("CUDA not available")
    except ImportError:
        issues.append("PyTorch not installed")
    
    if issues:
        print("Potential stability issues found:")
        for issue in issues:
            print(f"  ⚠️  {issue}")
    else:
        print("✓ No obvious stability issues detected")
    print()

def main():
    print("ComfyUI Environment Diagnostic Tool")
    print("=" * 50)
    print()
    
    check_python()
    check_pytorch()
    check_memory()
    check_processes()
    check_comfyui_deps()
    check_network()
    check_stability_issues()
    
    print("=== Recommendations ===")
    print("For better stability:")
    print("1. Use --normalvram instead of --highvram or --gpu-only")
    print("2. Increase --reserve-vram to 4.0 or higher")
    print("3. Disable --fast mode if experiencing crashes")
    print("4. Use --cache-classic instead of --cache-lru")
    print("5. Monitor GPU temperature and ensure good cooling")
    print("6. Restart ComfyUI periodically to clear memory leaks")

if __name__ == "__main__":
    main()
