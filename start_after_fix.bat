@echo off
title ComfyUI Stable Mode - After PyTorch Fix
color 0A

echo ==========================================
echo   ComfyUI Stable Mode - RTX 3090 24GB
echo ==========================================
echo.

echo Testing PyTorch installation...
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -c "import torch; print('PyTorch version:', torch.__version__); print('CUDA available:', torch.cuda.is_available())"

if %errorlevel% neq 0 (
    echo.
    echo ❌ PyTorch installation issue detected!
    echo Please wait for PyTorch reinstallation to complete.
    pause
    exit /b 1
)

echo.
echo ✅ PyTorch working correctly!
echo.

REM Clean up any existing processes
echo Cleaning up existing processes...
taskkill /f /im python.exe >nul 2>&1
timeout /t 2 >nul

REM Set stable environment variables
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
set HF_HUB_DISABLE_TELEMETRY=1
set DO_NOT_TRACK=1

echo Starting ComfyUI with stable settings...
echo Server URL: http://127.0.0.1:8188
echo.

C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --normalvram --use-pytorch-cross-attention --preview-method auto --cache-classic --reserve-vram 6.0 --listen 127.0.0.1 --port 8188 --auto-launch

echo.
echo ==========================================
echo ComfyUI Server Stopped
echo ==========================================
pause
