@echo off
title ComfyUI Auto-Restart Mode - RTX 3090 24GB
color 0C

:RESTART_LOOP
cls
echo ===============================================
echo        ComfyUI Auto-Restart Mode
echo      RTX 3090 24GB with Auto Recovery
echo ===============================================
echo.
echo Current Time: %date% %time%
echo Restart Count: %restart_count%
echo.

REM Initialize restart counter
if not defined restart_count set restart_count=0
set /a restart_count+=1

REM Kill any existing Python processes
echo [INFO] Cleaning up existing processes...
taskkill /f /im python.exe >nul 2>&1
timeout /t 3 >nul

REM Set ultra-stable environment variables
echo [INFO] Configuring ultra-stable environment...
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256,garbage_collection_threshold:0.8,roundup_power2_divisions:8
set CUDA_LAUNCH_BLOCKING=0
set TORCH_CUDNN_V8_API_ENABLED=1
set TORCH_BACKENDS_CUDNN_BENCHMARK=0
set HF_HUB_DISABLE_TELEMETRY=1
set DO_NOT_TRACK=1
set PYTHONUNBUFFERED=1
set OMP_NUM_THREADS=2
set MKL_NUM_THREADS=2

REM Conservative GPU settings
set TORCH_BACKENDS_CUDA_MATMUL_ALLOW_TF32=0
set TORCH_BACKENDS_CUDNN_ALLOW_TF32=0
set CUDA_VISIBLE_DEVICES=0

echo [INFO] Starting ComfyUI (Attempt #%restart_count%)...
echo [INFO] Server will auto-restart if it crashes
echo [INFO] Press Ctrl+C twice quickly to stop auto-restart
echo.

REM Launch ComfyUI with ultra-stable settings
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py ^
    --normalvram ^
    --use-pytorch-cross-attention ^
    --preview-method none ^
    --cache-classic ^
    --reserve-vram 6.0 ^
    --listen 127.0.0.1 ^
    --port 8188 ^
    --verbose INFO ^
    --dont-print-server ^
    --auto-launch

echo.
echo ===============================================
echo ComfyUI crashed or stopped at %time%
echo Auto-restarting in 10 seconds...
echo Press Ctrl+C to stop auto-restart
echo ===============================================
timeout /t 10

goto RESTART_LOOP
