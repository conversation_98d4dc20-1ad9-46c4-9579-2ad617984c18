@echo off
title ComfyUI System Diagnostics
color 0E

echo ===============================================
echo        ComfyUI System Diagnostics
echo ===============================================
echo.

echo [1/8] Checking Python installation...
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe --version
echo.

echo [2/8] Checking PyTorch installation...
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA Available: {torch.cuda.is_available()}'); print(f'CUDA Version: {torch.version.cuda}'); print(f'GPU Count: {torch.cuda.device_count()}'); print(f'GPU Name: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else \"N/A\"}')"
echo.

echo [3/8] Checking GPU memory...
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -c "import torch; print(f'GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB') if torch.cuda.is_available() else print('CUDA not available')"
echo.

echo [4/8] Checking system memory...
wmic computersystem get TotalPhysicalMemory /value | find "TotalPhysicalMemory"
echo.

echo [5/8] Checking running Python processes...
tasklist /fi "imagename eq python.exe" /fo table
echo.

echo [6/8] Checking network ports...
netstat -an | find "8188"
echo.

echo [7/8] Checking ComfyUI dependencies...
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -c "
try:
    import comfy
    print('✓ ComfyUI core modules loaded')
except Exception as e:
    print(f'✗ ComfyUI error: {e}')

try:
    import xformers
    print('✓ xformers available')
except:
    print('✗ xformers not available')

try:
    import accelerate
    print('✓ accelerate available')
except:
    print('✗ accelerate not available')
"
echo.

echo [8/8] Checking custom nodes...
if exist "custom_nodes" (
    echo Custom nodes directory found:
    dir custom_nodes /b
) else (
    echo No custom_nodes directory found
)
echo.

echo ===============================================
echo Diagnostics Complete
echo ===============================================
echo.
echo Common stability issues and solutions:
echo.
echo 1. GPU Memory Issues:
echo    - Use --normalvram or --lowvram instead of --highvram
echo    - Increase --reserve-vram value
echo    - Disable --fast mode
echo.
echo 2. Network Issues:
echo    - Use 127.0.0.1 instead of 0.0.0.0
echo    - Check firewall settings
echo    - Try different port
echo.
echo 3. Custom Node Issues:
echo    - Disable custom nodes with --disable-all-custom-nodes
echo    - Update or remove problematic nodes
echo.
echo 4. Memory Leaks:
echo    - Use --cache-classic instead of --cache-lru
echo    - Enable garbage collection
echo    - Restart service periodically
echo.
pause
