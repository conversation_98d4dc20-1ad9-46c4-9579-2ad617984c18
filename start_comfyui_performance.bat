@echo off
title ComfyUI High Performance Mode - RTX 3090 24GB
color 0A

echo ===============================================
echo        ComfyUI High Performance Mode
echo           RTX 3090 24GB Optimized
echo ===============================================
echo.
echo Starting ComfyUI with maximum performance...
echo Server URL: http://127.0.0.1:8188
echo.

REM Set high performance environment variables for RTX 3090 24GB
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:2048,expandable_segments:True
set CUDA_LAUNCH_BLOCKING=0
set TORCH_CUDNN_V8_API_ENABLED=1
set TORCH_BACKENDS_CUDNN_BENCHMARK=1
set TORCH_BACKENDS_CUDA_MATMUL_ALLOW_TF32=1
set TORCH_BACKENDS_CUDNN_ALLOW_TF32=1
set HF_HUB_DISABLE_TELEMETRY=1
set DO_NOT_TRACK=1
set OMP_NUM_THREADS=8

echo [INFO] Environment configured for maximum performance
echo [INFO] Starting ComfyUI...
echo.

REM Launch ComfyUI with high performance settings
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --gpu-only --use-pytorch-cross-attention --fast --mmap-torch-files --preview-method auto --disable-smart-memory --cache-lru 15 --fp16-vae --async-offload --listen 0.0.0.0 --port 8188 --auto-launch

echo.
echo ===============================================
echo ComfyUI High Performance Service Stopped
echo ===============================================
pause
