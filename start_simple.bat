@echo off
python -c "import sys; print(sys.version)" > nul 2>&1
if %errorlevel% neq 0 (
    echo Python not found! Please install Python 3.10 or newer.
    echo Visit: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Installing required packages...
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
pip install -r requirements.txt

echo Starting ComfyUI Server...
echo Server will be available at: http://127.0.0.1:8188
python main.py --port 8188

pause
