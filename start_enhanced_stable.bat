@echo off
title ComfyUI Enhanced Stable Mode - RTX 3090 24GB
color 0A

echo ===============================================
echo      ComfyUI Enhanced Stable Mode
echo        RTX 3090 24GB Optimized
echo        With Advanced Monitoring
echo ===============================================
echo.

REM Clean up any existing processes
echo [1/6] Cleaning up existing processes...
taskkill /f /im python.exe >nul 2>&1
timeout /t 3 >nul

REM Set optimized environment variables
echo [2/6] Setting optimized environment variables...
call set_stability_env.bat

REM Additional stability settings
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256,garbage_collection_threshold:0.9,roundup_power2_divisions:4
set CUDA_LAUNCH_BLOCKING=0
set TORCH_CUDNN_V8_API_ENABLED=1
set TORCH_BACKENDS_CUDNN_BENCHMARK=0

REM Conservative threading for stability
set OMP_NUM_THREADS=2
set MKL_NUM_THREADS=2
set NUMEXPR_NUM_THREADS=2

REM Disable aggressive optimizations
set TORCH_BACKENDS_CUDA_MATMUL_ALLOW_TF32=0
set TORCH_BACKENDS_CUDNN_ALLOW_TF32=0

echo [3/6] Starting memory monitor in background...
start /min "Memory Monitor" C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe memory_monitor.py

echo [4/6] Starting health checker in background...
start /min "Health Checker" C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe health_checker.py

echo [5/6] Waiting for monitors to initialize...
timeout /t 5 >nul

echo [6/6] Starting ComfyUI with enhanced stable settings...
echo.
echo ===============================================
echo Server URL: http://127.0.0.1:8188
echo Memory Monitor: Running in background
echo Health Checker: Running in background
echo Log Files: memory_log.jsonl, health_log.jsonl
echo ===============================================
echo.

REM Launch ComfyUI with enhanced stable settings
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py ^
    --normalvram ^
    --use-pytorch-cross-attention ^
    --preview-method none ^
    --cache-classic ^
    --reserve-vram 6.0 ^
    --listen 127.0.0.1 ^
    --port 8188 ^
    --verbose INFO ^
    --auto-launch

echo.
echo ===============================================
echo ComfyUI Enhanced Stable Service Stopped
echo Cleaning up background processes...
echo ===============================================

REM Clean up background processes
taskkill /f /im python.exe /fi "WINDOWTITLE eq Memory Monitor" >nul 2>&1
taskkill /f /im python.exe /fi "WINDOWTITLE eq Health Checker" >nul 2>&1

echo Background processes cleaned up.
pause
