@echo off
REM ComfyUI Stability Environment Variables

REM CUDA Memory Management
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256,garbage_collection_threshold:0.9,roundup_power2_divisions:4

REM PyTorch Optimizations
set TORCH_CUDNN_V8_API_ENABLED=1
set TORCH_BACKENDS_CUDNN_BENCHMARK=0
set TORCH_BACKENDS_CUDA_MATMUL_ALLOW_TF32=0
set TORCH_BACKENDS_CUDNN_ALLOW_TF32=0

REM Threading Control
set OMP_NUM_THREADS=2
set MKL_NUM_THREADS=2
set NUMEXPR_NUM_THREADS=2

REM Python Optimizations
set PYTHONUNBUFFERED=1
set PYTHONDONTWRITEBYTECODE=1

REM Disable Telemetry
set HF_HUB_DISABLE_TELEMETRY=1
set DO_NOT_TRACK=1

echo Environment variables set for maximum stability
