@echo off
title ComfyUI Ultra Stable Mode - RTX 3090 24GB
color 0A

echo ===============================================
echo      ComfyUI Ultra Stable Mode
echo        RTX 3090 24GB Optimized
echo ===============================================
echo.

REM Clean up any existing processes
echo [INFO] Cleaning up existing processes...
taskkill /f /im python.exe >nul 2>&1
timeout /t 3 >nul

REM Set ultra-stable environment variables
echo [INFO] Setting ultra-stable environment...
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256,garbage_collection_threshold:0.9,roundup_power2_divisions:4
set CUDA_LAUNCH_BLOCKING=0
set TORCH_CUDNN_V8_API_ENABLED=1
set TORCH_BACKENDS_CUDNN_BENCHMARK=0
set HF_HUB_DISABLE_TELEMETRY=1
set DO_NOT_TRACK=1
set PYTHONUNBUFFERED=1

REM Conservative threading
set OMP_NUM_THREADS=2
set MKL_NUM_THREADS=2
set NUMEXPR_NUM_THREADS=2

REM Disable aggressive optimizations
set TORCH_BACKENDS_CUDA_MATMUL_ALLOW_TF32=0
set TORCH_BACKENDS_CUDNN_ALLOW_TF32=0

echo [INFO] Starting ComfyUI with ultra-stable settings...
echo Server URL: http://127.0.0.1:8188
echo.

REM Launch ComfyUI with ultra-stable settings
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py ^
    --normalvram ^
    --use-pytorch-cross-attention ^
    --preview-method none ^
    --cache-classic ^
    --reserve-vram 8.0 ^
    --listen 127.0.0.1 ^
    --port 8188 ^
    --verbose INFO ^
    --disable-all-custom-nodes ^
    --auto-launch

echo.
echo ===============================================
echo ComfyUI Ultra Stable Service Stopped
echo ===============================================
pause
