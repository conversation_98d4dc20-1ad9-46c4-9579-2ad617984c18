#!/usr/bin/env python3
import psutil
import time
import json
from datetime import datetime

def monitor_memory():
    """Monitor system and GPU memory usage"""
    try:
        import torch
        gpu_available = torch.cuda.is_available()
    except ImportError:
        gpu_available = False
    
    while True:
        # System memory
        memory = psutil.virtual_memory()
        
        # GPU memory
        gpu_info = {}
        if gpu_available:
            try:
                for i in range(torch.cuda.device_count()):
                    allocated = torch.cuda.memory_allocated(i) / 1024**3
                    reserved = torch.cuda.memory_reserved(i) / 1024**3
                    total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                    gpu_info[f"gpu_{i}"] = {
                        "allocated_gb": round(allocated, 2),
                        "reserved_gb": round(reserved, 2),
                        "total_gb": round(total, 2),
                        "free_gb": round(total - reserved, 2)
                    }
            except Exception as e:
                gpu_info["error"] = str(e)
        
        # Log data
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "system_memory": {
                "total_gb": round(memory.total / 1024**3, 2),
                "available_gb": round(memory.available / 1024**3, 2),
                "used_gb": round(memory.used / 1024**3, 2),
                "percent": memory.percent
            },
            "gpu_memory": gpu_info
        }
        
        # Check for memory issues
        if memory.percent > 90:
            print(f"⚠️  HIGH MEMORY USAGE: {memory.percent:.1f}%")
        
        if gpu_available and gpu_info:
            for gpu_id, info in gpu_info.items():
                if isinstance(info, dict) and info.get("free_gb", 0) < 2:
                    print(f"⚠️  LOW GPU MEMORY: {gpu_id} has {info['free_gb']:.1f}GB free")
        
        # Write to log file
        with open("memory_log.jsonl", "a") as f:
            f.write(json.dumps(log_data) + "\n")
        
        time.sleep(30)  # Check every 30 seconds

if __name__ == "__main__":
    print("Starting memory monitor...")
    monitor_memory()
