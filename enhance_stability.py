#!/usr/bin/env python3
"""
ComfyUI Stability Enhancement Script
Optimizes system settings and creates monitoring tools
"""

import os
import sys
import json
import psutil
import time
from pathlib import Path

def create_stability_config():
    """Create optimized configuration for stability"""
    config = {
        "stability_mode": True,
        "memory_management": {
            "cuda_malloc_conf": "max_split_size_mb:256,garbage_collection_threshold:0.9",
            "reserve_vram_gb": 6.0,
            "enable_memory_monitoring": True,
            "auto_cleanup_interval": 300  # 5 minutes
        },
        "performance_settings": {
            "use_conservative_attention": True,
            "disable_fast_mode": True,
            "cache_type": "classic",
            "preview_method": "none",
            "vram_mode": "normal"
        },
        "network_settings": {
            "listen_address": "127.0.0.1",
            "port": 8188,
            "timeout_seconds": 30,
            "max_connections": 10
        },
        "logging": {
            "level": "INFO",
            "enable_performance_logging": True,
            "log_memory_usage": True
        }
    }
    
    with open("stability_config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print("✓ Created stability configuration file")

def create_memory_monitor():
    """Create memory monitoring script"""
    monitor_script = '''#!/usr/bin/env python3
import psutil
import time
import json
from datetime import datetime

def monitor_memory():
    """Monitor system and GPU memory usage"""
    try:
        import torch
        gpu_available = torch.cuda.is_available()
    except ImportError:
        gpu_available = False
    
    while True:
        # System memory
        memory = psutil.virtual_memory()
        
        # GPU memory
        gpu_info = {}
        if gpu_available:
            try:
                for i in range(torch.cuda.device_count()):
                    allocated = torch.cuda.memory_allocated(i) / 1024**3
                    reserved = torch.cuda.memory_reserved(i) / 1024**3
                    total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                    gpu_info[f"gpu_{i}"] = {
                        "allocated_gb": round(allocated, 2),
                        "reserved_gb": round(reserved, 2),
                        "total_gb": round(total, 2),
                        "free_gb": round(total - reserved, 2)
                    }
            except Exception as e:
                gpu_info["error"] = str(e)
        
        # Log data
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "system_memory": {
                "total_gb": round(memory.total / 1024**3, 2),
                "available_gb": round(memory.available / 1024**3, 2),
                "used_gb": round(memory.used / 1024**3, 2),
                "percent": memory.percent
            },
            "gpu_memory": gpu_info
        }
        
        # Check for memory issues
        if memory.percent > 90:
            print(f"⚠️  HIGH MEMORY USAGE: {memory.percent:.1f}%")
        
        if gpu_available and gpu_info:
            for gpu_id, info in gpu_info.items():
                if isinstance(info, dict) and info.get("free_gb", 0) < 2:
                    print(f"⚠️  LOW GPU MEMORY: {gpu_id} has {info['free_gb']:.1f}GB free")
        
        # Write to log file
        with open("memory_log.jsonl", "a") as f:
            f.write(json.dumps(log_data) + "\\n")
        
        time.sleep(30)  # Check every 30 seconds

if __name__ == "__main__":
    print("Starting memory monitor...")
    monitor_memory()
'''
    
    with open("memory_monitor.py", "w", encoding='utf-8') as f:
        f.write(monitor_script)
    
    print("✓ Created memory monitoring script")

def create_health_checker():
    """Create health checking script"""
    health_script = '''#!/usr/bin/env python3
import requests
import time
import json
from datetime import datetime

def check_comfyui_health():
    """Check ComfyUI server health"""
    url = "http://127.0.0.1:8188"
    
    while True:
        try:
            response = requests.get(f"{url}/system_stats", timeout=5)
            if response.status_code == 200:
                print(f"✓ ComfyUI healthy at {datetime.now().strftime('%H:%M:%S')}")
                status = "healthy"
            else:
                print(f"⚠️  ComfyUI responding but status {response.status_code}")
                status = "warning"
        except requests.exceptions.ConnectionError:
            print(f"❌ ComfyUI not responding at {datetime.now().strftime('%H:%M:%S')}")
            status = "down"
        except Exception as e:
            print(f"❌ Health check error: {e}")
            status = "error"
        
        # Log health status
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "status": status
        }
        
        with open("health_log.jsonl", "a") as f:
            f.write(json.dumps(log_data) + "\\n")
        
        time.sleep(60)  # Check every minute

if __name__ == "__main__":
    print("Starting health checker...")
    check_comfyui_health()
'''
    
    with open("health_checker.py", "w", encoding='utf-8') as f:
        f.write(health_script)
    
    print("✓ Created health checking script")

def optimize_system_settings():
    """Optimize Windows system settings for stability"""
    print("Optimizing system settings...")
    
    # Create optimized environment variables script
    env_script = '''@echo off
REM ComfyUI Stability Environment Variables

REM CUDA Memory Management
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256,garbage_collection_threshold:0.9,roundup_power2_divisions:4

REM PyTorch Optimizations
set TORCH_CUDNN_V8_API_ENABLED=1
set TORCH_BACKENDS_CUDNN_BENCHMARK=0
set TORCH_BACKENDS_CUDA_MATMUL_ALLOW_TF32=0
set TORCH_BACKENDS_CUDNN_ALLOW_TF32=0

REM Threading Control
set OMP_NUM_THREADS=2
set MKL_NUM_THREADS=2
set NUMEXPR_NUM_THREADS=2

REM Python Optimizations
set PYTHONUNBUFFERED=1
set PYTHONDONTWRITEBYTECODE=1

REM Disable Telemetry
set HF_HUB_DISABLE_TELEMETRY=1
set DO_NOT_TRACK=1

echo Environment variables set for maximum stability
'''
    
    with open("set_stability_env.bat", "w", encoding='utf-8') as f:
        f.write(env_script)
    
    print("✓ Created environment optimization script")

def main():
    print("ComfyUI Stability Enhancement")
    print("=" * 40)
    print()
    
    # Create configuration files
    create_stability_config()
    create_memory_monitor()
    create_health_checker()
    optimize_system_settings()
    
    print()
    print("✅ Stability enhancement complete!")
    print()
    print("Created files:")
    print("  - stability_config.json (configuration)")
    print("  - memory_monitor.py (memory monitoring)")
    print("  - health_checker.py (health checking)")
    print("  - set_stability_env.bat (environment setup)")
    print()
    print("Next steps:")
    print("1. Run: set_stability_env.bat")
    print("2. Start monitoring: python memory_monitor.py")
    print("3. Start health checking: python health_checker.py")
    print("4. Use the enhanced stable launcher")

if __name__ == "__main__":
    main()
