@echo off
title ComfyUI 智能启动器 - 性能模式选择
color 0B
chcp 65001 >nul

:MENU
cls
echo ╔══════════════════════════════════════════════════════════════════════════╗
echo ║                        ComfyUI 智能启动器                                ║
echo ║                     RTX 3090 24GB 性能优化版                            ║
echo ╠══════════════════════════════════════════════════════════════════════════╣
echo ║                                                                          ║
echo ║  请选择启动模式:                                                         ║
echo ║                                                                          ║
echo ║  [1] 🚀 超高性能模式 (推荐)                                              ║
echo ║      • 最大化GPU利用率                                                   ║
echo ║      • 所有优化启用                                                      ║
echo ║      • 适合: 专业工作流程                                                ║
echo ║                                                                          ║
echo ║  [2] ⚡ 高性能模式                                                       ║
echo ║      • 平衡性能与稳定性                                                  ║
echo ║      • 大部分优化启用                                                    ║
echo ║      • 适合: 日常使用                                                    ║
echo ║                                                                          ║
echo ║  [3] 🔧 标准模式                                                         ║
echo ║      • 默认配置                                                          ║
echo ║      • 基础优化                                                          ║
echo ║      • 适合: 测试和调试                                                  ║
echo ║                                                                          ║
echo ║  [4] 💾 低显存模式                                                       ║
echo ║      • 节省显存使用                                                      ║
echo ║      • 降低性能换取稳定性                                                ║
echo ║      • 适合: 多任务环境                                                  ║
echo ║                                                                          ║
echo ║  [5] 🖥️  CPU模式                                                         ║
echo ║      • 纯CPU计算                                                         ║
echo ║      • 不使用GPU                                                         ║
echo ║      • 适合: GPU故障时                                                   ║
echo ║                                                                          ║
echo ║  [Q] 退出                                                                ║
echo ║                                                                          ║
echo ╚══════════════════════════════════════════════════════════════════════════╝
echo.
set /p choice=请输入选择 (1-5 或 Q): 

if /i "%choice%"=="1" goto ULTRA_PERFORMANCE
if /i "%choice%"=="2" goto HIGH_PERFORMANCE
if /i "%choice%"=="3" goto STANDARD
if /i "%choice%"=="4" goto LOW_VRAM
if /i "%choice%"=="5" goto CPU_MODE
if /i "%choice%"=="Q" goto EXIT
if /i "%choice%"=="q" goto EXIT

echo 无效选择，请重新输入...
timeout /t 2 >nul
goto MENU

:ULTRA_PERFORMANCE
cls
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    超高性能模式启动中...                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo [INFO] 配置超高性能环境变量...

REM 超高性能环境变量配置
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:2048,expandable_segments:True,roundup_power2_divisions:16
set CUDA_LAUNCH_BLOCKING=0
set TORCH_CUDNN_V8_API_ENABLED=1
set TORCH_BACKENDS_CUDNN_BENCHMARK=1
set TORCH_BACKENDS_CUDA_MATMUL_ALLOW_TF32=1
set TORCH_BACKENDS_CUDNN_ALLOW_TF32=1
set HF_HUB_DISABLE_TELEMETRY=1
set DO_NOT_TRACK=1
set OMP_NUM_THREADS=8

echo [INFO] 启动ComfyUI超高性能模式...
echo 服务地址: http://127.0.0.1:8188
echo.

C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py ^
    --gpu-only ^
    --use-pytorch-cross-attention ^
    --fast ^
    --mmap-torch-files ^
    --preview-method auto ^
    --disable-smart-memory ^
    --cache-lru 15 ^
    --fp16-vae ^
    --async-offload ^
    --auto-launch
goto END

:HIGH_PERFORMANCE
cls
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                     高性能模式启动中...                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo [INFO] 配置高性能环境变量...

set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:1024,expandable_segments:True
set CUDA_LAUNCH_BLOCKING=0
set TORCH_CUDNN_V8_API_ENABLED=1
set TORCH_BACKENDS_CUDNN_BENCHMARK=1
set HF_HUB_DISABLE_TELEMETRY=1

echo [INFO] 启动ComfyUI高性能模式...
echo 服务地址: http://127.0.0.1:8188
echo.

C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py ^
    --highvram ^
    --fast ^
    --mmap-torch-files ^
    --preview-method auto ^
    --cache-lru 10 ^
    --auto-launch
goto END

:STANDARD
cls
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      标准模式启动中...                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo [INFO] 启动ComfyUI标准模式...
echo 服务地址: http://127.0.0.1:8188
echo.

C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --auto-launch
goto END

:LOW_VRAM
cls
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                     低显存模式启动中...                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo [INFO] 启动ComfyUI低显存模式...
echo 服务地址: http://127.0.0.1:8188
echo 注意: 此模式会降低性能但减少显存使用
echo.

C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --lowvram --auto-launch
goto END

:CPU_MODE
cls
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      CPU模式启动中...                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo [INFO] 启动ComfyUI CPU模式...
echo 服务地址: http://127.0.0.1:8188
echo 注意: 此模式速度较慢但不需要GPU
echo.

C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --cpu --auto-launch
goto END

:EXIT
echo 退出启动器...
exit /b 0

:END
echo.
echo ═══════════════════════════════════════════════════════════════
echo ComfyUI服务已停止
echo ═══════════════════════════════════════════════════════════════
echo.
echo 按任意键返回主菜单，或关闭窗口退出...
pause >nul
goto MENU
