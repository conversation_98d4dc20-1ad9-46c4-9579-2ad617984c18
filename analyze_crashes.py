#!/usr/bin/env python3
"""
ComfyUI Crash Analysis Tool
Analyzes system state and identifies potential crash causes
"""

import os
import sys
import json
import psutil
import time
import subprocess
from datetime import datetime

def check_gpu_stability():
    """Check GPU stability and temperature"""
    print("=== GPU Stability Check ===")
    try:
        import torch
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                allocated = torch.cuda.memory_allocated(i) / 1024**3
                reserved = torch.cuda.memory_reserved(i) / 1024**3
                total = props.total_memory / 1024**3
                
                print(f"GPU {i}: {props.name}")
                print(f"  Memory: {allocated:.1f}GB allocated, {reserved:.1f}GB reserved, {total:.1f}GB total")
                print(f"  Free: {total - reserved:.1f}GB")
                
                # Check for memory fragmentation
                if reserved > allocated * 1.5:
                    print(f"  ⚠️  Memory fragmentation detected (reserved >> allocated)")
                
                # Check for low memory
                if (total - reserved) < 2:
                    print(f"  ❌ Very low GPU memory available")
                elif (total - reserved) < 4:
                    print(f"  ⚠️  Low GPU memory available")
                else:
                    print(f"  ✓ GPU memory OK")
        else:
            print("❌ CUDA not available")
    except Exception as e:
        print(f"❌ GPU check failed: {e}")
    print()

def check_system_resources():
    """Check system resource usage"""
    print("=== System Resources ===")
    
    # Memory
    memory = psutil.virtual_memory()
    print(f"RAM: {memory.used/1024**3:.1f}GB used / {memory.total/1024**3:.1f}GB total ({memory.percent:.1f}%)")
    
    if memory.percent > 90:
        print("❌ Critical memory usage")
    elif memory.percent > 80:
        print("⚠️  High memory usage")
    else:
        print("✓ Memory usage OK")
    
    # CPU
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"CPU: {cpu_percent:.1f}% usage")
    
    if cpu_percent > 90:
        print("❌ Critical CPU usage")
    elif cpu_percent > 80:
        print("⚠️  High CPU usage")
    else:
        print("✓ CPU usage OK")
    
    # Disk
    disk = psutil.disk_usage('.')
    print(f"Disk: {disk.used/1024**3:.1f}GB used / {disk.total/1024**3:.1f}GB total ({disk.used/disk.total*100:.1f}%)")
    
    if disk.used/disk.total > 0.95:
        print("❌ Critical disk space")
    elif disk.used/disk.total > 0.90:
        print("⚠️  Low disk space")
    else:
        print("✓ Disk space OK")
    
    print()

def check_python_processes():
    """Check for multiple Python processes"""
    print("=== Python Processes ===")
    python_procs = []
    
    for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent', 'cmdline']):
        try:
            if 'python' in proc.info['name'].lower():
                python_procs.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    if len(python_procs) > 3:
        print(f"⚠️  Many Python processes running ({len(python_procs)})")
        print("This could indicate process leaks or conflicts")
    elif len(python_procs) == 0:
        print("✓ No Python processes running")
    else:
        print(f"✓ {len(python_procs)} Python processes running")
    
    for proc in python_procs:
        memory_mb = proc['memory_info'].rss / 1024 / 1024
        cmdline = ' '.join(proc.get('cmdline', []))[:80] + '...' if len(' '.join(proc.get('cmdline', []))) > 80 else ' '.join(proc.get('cmdline', []))
        print(f"  PID {proc['pid']}: {memory_mb:.1f}MB - {cmdline}")
    
    print()

def check_crash_patterns():
    """Analyze crash patterns from logs"""
    print("=== Crash Pattern Analysis ===")
    
    crash_indicators = [
        "CUDA out of memory",
        "RuntimeError",
        "MemoryError",
        "OutOfMemoryError",
        "CUDA error",
        "Connection broken",
        "Process finished with exit code"
    ]
    
    # Check if there are any log files
    log_files = []
    for file in os.listdir('.'):
        if file.endswith('.log') or file.endswith('.jsonl'):
            log_files.append(file)
    
    if not log_files:
        print("No log files found for analysis")
        print("Recommendation: Enable verbose logging")
    else:
        print(f"Found {len(log_files)} log files")
        # Simple analysis would go here
        print("Log files available for manual review:")
        for log_file in log_files:
            print(f"  - {log_file}")
    
    print()

def generate_recommendations():
    """Generate stability recommendations"""
    print("=== Stability Recommendations ===")
    
    recommendations = []
    
    # Check memory
    memory = psutil.virtual_memory()
    if memory.percent > 80:
        recommendations.append("Reduce system memory usage before starting ComfyUI")
        recommendations.append("Close unnecessary applications")
    
    # Check GPU memory
    try:
        import torch
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                total = props.total_memory / 1024**3
                if total < 8:
                    recommendations.append(f"GPU {i} has limited VRAM ({total:.1f}GB) - use --lowvram mode")
                elif total < 12:
                    recommendations.append(f"GPU {i} has moderate VRAM ({total:.1f}GB) - use --normalvram mode")
    except:
        pass
    
    # General recommendations
    recommendations.extend([
        "Use --lowvram for maximum stability",
        "Increase --reserve-vram to 8.0 or higher",
        "Disable --fast mode and aggressive optimizations",
        "Use --cache-classic instead of --cache-lru",
        "Disable custom nodes if experiencing crashes",
        "Monitor GPU temperature and ensure good cooling",
        "Restart ComfyUI every 2-3 hours to prevent memory leaks",
        "Use --preview-method none to reduce memory usage"
    ])
    
    for i, rec in enumerate(recommendations, 1):
        print(f"{i}. {rec}")
    
    print()

def create_crash_report():
    """Create a detailed crash report"""
    report = {
        "timestamp": datetime.now().isoformat(),
        "system_info": {
            "platform": sys.platform,
            "python_version": sys.version,
            "total_ram_gb": psutil.virtual_memory().total / 1024**3,
            "available_ram_gb": psutil.virtual_memory().available / 1024**3,
            "cpu_count": psutil.cpu_count(),
            "cpu_percent": psutil.cpu_percent()
        }
    }
    
    # Add GPU info
    try:
        import torch
        if torch.cuda.is_available():
            gpu_info = {}
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                gpu_info[f"gpu_{i}"] = {
                    "name": props.name,
                    "total_memory_gb": props.total_memory / 1024**3,
                    "compute_capability": f"{props.major}.{props.minor}"
                }
            report["gpu_info"] = gpu_info
    except:
        report["gpu_info"] = {"error": "Could not get GPU info"}
    
    # Save report
    with open(f"crash_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print(f"✓ Crash report saved to crash_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

def main():
    print("ComfyUI Crash Analysis Tool")
    print("=" * 50)
    print()
    
    check_gpu_stability()
    check_system_resources()
    check_python_processes()
    check_crash_patterns()
    generate_recommendations()
    create_crash_report()
    
    print("=" * 50)
    print("Analysis complete!")
    print()
    print("If crashes persist, consider:")
    print("1. Using start_crash_resistant.bat")
    print("2. Reducing model complexity")
    print("3. Checking GPU temperature")
    print("4. Updating GPU drivers")

if __name__ == "__main__":
    main()
