@echo off
title ComfyUI System Optimization
color 0E

echo ===============================================
echo        ComfyUI System Optimization
echo ===============================================
echo.
echo This script will optimize your system for
echo maximum ComfyUI stability and performance.
echo.
echo WARNING: This will modify system settings.
echo Press any key to continue or Ctrl+C to cancel...
pause >nul

echo.
echo [1/8] Setting Windows power plan to High Performance...
powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Power plan set to High Performance
) else (
    echo ⚠ Could not set power plan (may require admin rights)
)

echo.
echo [2/8] Optimizing virtual memory settings...
echo Checking current virtual memory configuration...
wmic computersystem get TotalPhysicalMemory /value | find "TotalPhysicalMemory" >temp_mem.txt
for /f "tokens=2 delims==" %%a in (temp_mem.txt) do set total_mem=%%a
del temp_mem.txt >nul 2>&1
set /a recommended_vm=%total_mem:~0,-9%*2
echo Recommended virtual memory: %recommended_vm%GB
echo ⚠ Please manually set virtual memory to %recommended_vm%GB in System Properties

echo.
echo [3/8] Disabling Windows visual effects for performance...
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects" /v VisualFXSetting /t REG_DWORD /d 2 /f >nul 2>&1
echo ✓ Visual effects optimized for performance

echo.
echo [4/8] Setting GPU performance mode...
echo Please manually set your NVIDIA GPU to "Prefer Maximum Performance" in NVIDIA Control Panel
echo Path: NVIDIA Control Panel > Manage 3D Settings > Power Management Mode

echo.
echo [5/8] Optimizing Windows Defender exclusions...
echo Adding ComfyUI directory to Windows Defender exclusions...
powershell -Command "Add-MpPreference -ExclusionPath '%cd%'" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Added ComfyUI directory to Defender exclusions
) else (
    echo ⚠ Could not add Defender exclusion (may require admin rights)
)

echo.
echo [6/8] Setting process priority optimization...
echo Creating process priority script...
echo @echo off > set_priority.bat
echo echo Setting ComfyUI process priority to High... >> set_priority.bat
echo wmic process where name="python.exe" CALL setpriority "high priority" >> set_priority.bat
echo ✓ Process priority script created

echo.
echo [7/8] Creating system monitoring dashboard...
echo @echo off > system_dashboard.bat
echo title ComfyUI System Dashboard >> system_dashboard.bat
echo :LOOP >> system_dashboard.bat
echo cls >> system_dashboard.bat
echo echo =============================================== >> system_dashboard.bat
echo echo        ComfyUI System Dashboard >> system_dashboard.bat
echo echo =============================================== >> system_dashboard.bat
echo echo. >> system_dashboard.bat
echo echo System Time: %%date%% %%time%% >> system_dashboard.bat
echo echo. >> system_dashboard.bat
echo echo === Memory Usage === >> system_dashboard.bat
echo wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /value ^| find "=" >> system_dashboard.bat
echo echo. >> system_dashboard.bat
echo echo === GPU Processes === >> system_dashboard.bat
echo tasklist /fi "imagename eq python.exe" /fo table >> system_dashboard.bat
echo echo. >> system_dashboard.bat
echo echo === Network Status === >> system_dashboard.bat
echo netstat -an ^| findstr 8188 >> system_dashboard.bat
echo echo. >> system_dashboard.bat
echo echo Press Ctrl+C to exit, or wait 30 seconds for refresh... >> system_dashboard.bat
echo timeout /t 30 ^>nul >> system_dashboard.bat
echo goto LOOP >> system_dashboard.bat
echo ✓ System dashboard created

echo.
echo [8/8] Creating maintenance scripts...

REM Create cleanup script
echo @echo off > cleanup_comfyui.bat
echo title ComfyUI Cleanup >> cleanup_comfyui.bat
echo echo Cleaning up ComfyUI temporary files... >> cleanup_comfyui.bat
echo taskkill /f /im python.exe ^>nul 2^>^&1 >> cleanup_comfyui.bat
echo if exist temp rmdir /s /q temp >> cleanup_comfyui.bat
echo if exist __pycache__ rmdir /s /q __pycache__ >> cleanup_comfyui.bat
echo for /d %%%%i in (*__pycache__*) do rmdir /s /q "%%%%i" >> cleanup_comfyui.bat
echo echo Cleanup complete! >> cleanup_comfyui.bat
echo pause >> cleanup_comfyui.bat

REM Create backup script
echo @echo off > backup_config.bat
echo title ComfyUI Configuration Backup >> backup_config.bat
echo set backup_dir=backup_%%date:~0,4%%%%date:~5,2%%%%date:~8,2%% >> backup_config.bat
echo mkdir %%backup_dir%% >> backup_config.bat
echo copy *.json %%backup_dir%%\ ^>nul 2^>^&1 >> backup_config.bat
echo copy *.bat %%backup_dir%%\ ^>nul 2^>^&1 >> backup_config.bat
echo copy *.py %%backup_dir%%\ ^>nul 2^>^&1 >> backup_config.bat
echo echo Configuration backed up to %%backup_dir%% >> backup_config.bat
echo pause >> backup_config.bat

echo ✓ Maintenance scripts created

echo.
echo ===============================================
echo System Optimization Complete!
echo ===============================================
echo.
echo Created files:
echo   - set_priority.bat (process priority)
echo   - system_dashboard.bat (monitoring)
echo   - cleanup_comfyui.bat (maintenance)
echo   - backup_config.bat (backup)
echo.
echo Manual steps required:
echo   1. Set NVIDIA GPU to "Prefer Maximum Performance"
echo   2. Adjust virtual memory if needed
echo   3. Run as administrator for full optimization
echo.
echo Recommended usage:
echo   1. Run system_dashboard.bat to monitor
echo   2. Run cleanup_comfyui.bat weekly
echo   3. Run backup_config.bat before changes
echo.
pause
