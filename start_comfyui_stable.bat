@echo off
title ComfyUI Stable Mode - RTX 3090 24GB
color 0B

echo ===============================================
echo        ComfyUI Stable Mode
echo      RTX 3090 24GB Stability Optimized
echo ===============================================
echo.
echo Configuring for maximum stability...
echo Server URL: http://127.0.0.1:8188
echo.

REM Kill any existing Python processes to avoid conflicts
echo [INFO] Cleaning up existing processes...
taskkill /f /im python.exe >nul 2>&1
timeout /t 2 >nul

REM Set stable environment variables - Conservative settings for RTX 3090
echo [INFO] Setting stable environment variables...
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512,garbage_collection_threshold:0.6
set CUDA_LAUNCH_BLOCKING=0
set TORCH_CUDNN_V8_API_ENABLED=1
set TORCH_BACKENDS_CUDNN_BENCHMARK=0
set HF_HUB_DISABLE_TELEMETRY=1
set DO_NOT_TRACK=1
set PYTHONUNBUFFERED=1
set OMP_NUM_THREADS=4
set MKL_NUM_THREADS=4

REM Disable problematic optimizations for stability
set TORCH_BACKENDS_CUDA_MATMUL_ALLOW_TF32=0
set TORCH_BACKENDS_CUDNN_ALLOW_TF32=0

echo [INFO] Environment configured for stability
echo [INFO] Starting ComfyUI with stable settings...
echo.

REM Launch ComfyUI with stability-focused settings
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py ^
    --highvram ^
    --use-pytorch-cross-attention ^
    --preview-method auto ^
    --cache-lru 5 ^
    --reserve-vram 4.0 ^
    --listen 127.0.0.1 ^
    --port 8188 ^
    --verbose INFO ^
    --auto-launch

echo.
echo ===============================================
echo ComfyUI Stable Service Stopped
echo ===============================================
echo Press any key to restart or close window to exit...
pause >nul
goto :eof
