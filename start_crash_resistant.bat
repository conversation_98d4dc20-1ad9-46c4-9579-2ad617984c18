@echo off
title ComfyUI Crash-Resistant Mode - RTX 3090 24GB
color 0D

echo ===============================================
echo      ComfyUI Crash-Resistant Mode
echo        Maximum Stability Priority
echo ===============================================
echo.

REM Force kill all Python processes
echo [1/5] Force cleaning all Python processes...
taskkill /f /im python.exe >nul 2>&1
timeout /t 5 >nul

REM Clear GPU memory
echo [2/5] Clearing GPU memory...
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -c "import torch; torch.cuda.empty_cache() if torch.cuda.is_available() else None" >nul 2>&1

REM Set ultra-conservative environment
echo [3/5] Setting ultra-conservative environment...
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:64,garbage_collection_threshold:0.99
set CUDA_LAUNCH_BLOCKING=1
set TORCH_CUDNN_V8_API_ENABLED=0
set TORCH_BACKENDS_CUDNN_BENCHMARK=0
set TORCH_BACKENDS_CUDA_MATMUL_ALLOW_TF32=0
set TORCH_BACKENDS_CUDNN_ALLOW_TF32=0
set OMP_NUM_THREADS=1
set MKL_NUM_THREADS=1
set NUMEXPR_NUM_THREADS=1
set HF_HUB_DISABLE_TELEMETRY=1
set DO_NOT_TRACK=1
set PYTHONUNBUFFERED=1

echo [4/5] Waiting for system stabilization...
timeout /t 10 >nul

echo [5/5] Starting ComfyUI in crash-resistant mode...
echo.
echo ===============================================
echo Configuration: Ultra-Conservative
echo VRAM Mode: LOW_VRAM (safest)
echo Reserved VRAM: 12GB (50% of total)
echo Cache: Classic (no memory leaks)
echo Optimizations: ALL DISABLED
echo Server URL: http://127.0.0.1:8188
echo ===============================================
echo.

REM Launch with maximum stability settings
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py ^
    --lowvram ^
    --use-pytorch-cross-attention ^
    --preview-method none ^
    --cache-classic ^
    --reserve-vram 12.0 ^
    --listen 127.0.0.1 ^
    --port 8188 ^
    --verbose INFO ^
    --disable-all-custom-nodes ^
    --auto-launch

echo.
echo ===============================================
echo ComfyUI Crash-Resistant Service Stopped
echo ===============================================
pause
